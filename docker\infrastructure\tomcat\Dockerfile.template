# 
# 注意：这是一个模板文件，不要直接使用
# 
# 基础镜像将由构建脚本替换
ARG BASE_IMAGE
FROM $BASE_IMAGE

# 显示基础镜像的环境变量（用于调试和验证继承）
RUN echo "=== 基础镜像环境变量 ===" && \
    env | sort && \
    echo "=== JAVA 相关环境变量 ===" && \
    env | grep -E "(JAVA|JDK|JRE)" || echo "未找到 JAVA 相关环境变量" && \
    echo "=========================="

# 设置环境变量
# 强制设置正确的 JAVA_HOME，避免被基础镜像中的错误值覆盖
ENV JAVA_HOME=/usr/local/jdk1.8.0_172 \
    JAVA_TOOL_OPTIONS=${JAVA_TOOL_OPTIONS:-} \
    TOMCAT_VERSION=$TOMCAT_VERSION \
    TOMCAT_MAJOR=$TOMCAT_MAJOR \
    TOMCAT_VARIANT=$TOMCAT_VARIANT \
    TOMCAT_HOME=/usr/local/tomcat \
    CATALINA_HOME=/usr/local/tomcat \
    CATALINA_BASE=/usr/local/tomcat \
    TOMCAT_NATIVE_LIBDIR=/usr/local/tomcat/native-jni-lib \
    LD_LIBRARY_PATH=/usr/lib64:/usr/local/tomcat/native-jni-lib:${LD_LIBRARY_PATH:-} \
    CRYPTO_FILES_VERSION=$CRYPTO_FILES_VERSION

# 修复 PATH，避免重复的 Java 路径
ENV PATH=/usr/local/tomcat/bin:$PATH

# 确保 shell 环境中也正确设置 JAVA_HOME
RUN echo 'export JAVA_HOME=/usr/local/jdk1.8.0_172' >> /etc/bash.bashrc && \
    echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /etc/bash.bashrc && \
    chown -R root:root $JAVA_HOME && \
    chmod -R a+rX $JAVA_HOME && \
    chmod +x $JAVA_HOME/bin/*

# 验证环境变量和 Java 安装
RUN echo "=== Tomcat 镜像环境变量 ===" && \
    echo "JAVA_HOME=$JAVA_HOME" && \
    echo "PATH=$PATH" && \
    echo "LD_LIBRARY_PATH=$LD_LIBRARY_PATH" && \
    echo "JAVA_TOOL_OPTIONS=$JAVA_TOOL_OPTIONS" && \
    echo "=== Java 可执行文件检查 ===" && \
    ls -la "$JAVA_HOME/bin/java" 2>/dev/null || echo "错误: $JAVA_HOME/bin/java 不存在" && \
    which java || echo "错误: java 命令未找到" && \
    echo "=== Java 版本测试 ===" && \
    java -version 2>&1 || echo "错误: java -version 失败" && \
    echo "=========================="

# 设置工作目录
WORKDIR $CATALINA_HOME

# 设置SHA512校验和（由构建脚本替换）
ENV TOMCAT_SHA512=$TOMCAT_SHA512

# 复制预下载的Tomcat包
COPY docker/infrastructure/tomcat/resources/apache-tomcat-${TOMCAT_VERSION}.tar.gz tomcat.tar.gz

# 验证校验和并解压
RUN set -eux; \
    if [ -n "$TOMCAT_SHA512" ]; then \
        echo "$TOMCAT_SHA512 *tomcat.tar.gz" | sha512sum --strict --check -; \
    fi; \
    tar -xf tomcat.tar.gz --strip-components=1; \
    rm tomcat.tar.gz; \
    \
    # 添加可执行权限
    chmod +x bin/*.sh; \
    \
    # 简单验证安装 - 只检查关键文件是否存在
    echo "验证Tomcat安装..."; \
    if [ ! -f "$CATALINA_HOME/bin/catalina.sh" ] || \
       [ ! -f "$CATALINA_HOME/conf/server.xml" ] || \
       [ ! -f "$CATALINA_HOME/lib/catalina.jar" ] || \
       [ ! -d "$CATALINA_HOME/webapps" ]; then \
        echo "Tomcat安装不完整，缺少关键文件"; \
        exit 1; \
    fi; \
    echo "Tomcat安装验证通过"

# 创建tomcat用户
RUN set -eux; \
    groupadd -r tomcat --gid=1000; \
    useradd -r -g tomcat --uid=1000 --home-dir=/usr/local/tomcat --shell=/bin/bash tomcat

# Copy NetcaJCrypto jar files (only for crypto variant)
COPY docker/infrastructure/tomcat/shared/crypto-files/slf4j* $CATALINA_HOME/lib/
COPY docker/infrastructure/tomcat/shared/crypto-files/NetcaJCrypto* $CATALINA_HOME/lib/

# 仅复制自定义的server.xml配置文件
COPY docker/infrastructure/tomcat/shared/conf/server.xml $CATALINA_HOME/conf/server.xml

# 设置所有权和权限
RUN set -eux; \
    # 确保所有文件属于tomcat用户
    chown -R tomcat:tomcat /usr/local/tomcat; \
    # 设置目录权限：所有者可读写执行，组和其他用户可读执行
    find /usr/local/tomcat -type d -exec chmod 755 {} \;; \
    # 设置文件权限：所有者可读写，组和其他用户可读，脚本文件可执行
    find /usr/local/tomcat -type f -exec chmod 644 {} \;; \
    # 确保所有.sh脚本文件可执行
    find /usr/local/tomcat -name "*.sh" -exec chmod 755 {} \;; \
    # 设置特殊目录权限（临时目录需要写权限）
    chmod 1777 logs temp work; \
    # 修复 JDK 目录权限，确保 tomcat 用户可以访问
    if [ -d "$JAVA_HOME" ]; then \
        echo "修复 JDK 目录权限: $JAVA_HOME"; \
        chmod -R a+rX "$JAVA_HOME"; \
        ls -la "$JAVA_HOME/bin/java"; \
    else \
        echo "警告: JAVA_HOME 目录不存在: $JAVA_HOME"; \
    fi

# 添加标签信息
LABEL maintainer="Medical Products Team" \
      description="Tomcat $TOMCAT_VERSION server with $JDK_VERSION for medical products" \
      version="$TOMCAT_VERSION" \
      build.date="$BUILD_DATE" \
      vendor="BTIT" \
      architecture="$ARCH" \
      base.image="$BASE_IMAGE" \
      java.type="$JAVA_TYPE" \
      java.distribution="$JAVA_DISTRIBUTION" \
      os.codename="$OS_CODENAME"

# 暴露端口
EXPOSE 8080 443 8443 8005 8009

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:8080/ || exit 1

# 启动Tomcat
USER tomcat
CMD ["/usr/local/tomcat/bin/catalina.sh", "run"]

