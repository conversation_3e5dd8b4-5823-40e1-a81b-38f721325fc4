#!/bin/bash
# ===============================================================
# 脚本名称: build-tomcat.sh
# 描述: 构建 Tomcat Docker 镜像脚本
# ===============================================================

set -euo pipefail

# 全局常量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
readonly CONFIG_DIR="${SCRIPT_DIR}/config"
readonly CONFIG_FILE="${CONFIG_DIR}/harbor.conf"
readonly VERSION_FILE="${SCRIPT_DIR}/versions/tomcat_versions.json"
readonly BASE_DIR="${PROJECT_ROOT}/docker/infrastructure/tomcat"

# 导入依赖库
source "${LIB_DIR}/logger.sh"
source "${LIB_DIR}/utils.sh"
source "${LIB_DIR}/docker.sh"
source "${LIB_DIR}/download.sh"

# 设置Harbor仓库地址（优先级：命令行参数 > 环境变量 > 配置文件 > 默认值）
# 首先检查是否存在配置文件
if [ -f "$CONFIG_FILE" ]; then
    # 从配置文件读取
    source "$CONFIG_FILE"
fi

# 然后检查环境变量（覆盖配置文件）
HARBOR_REGISTRY=${HARBOR_REGISTRY:-${HARBOR_REGISTRY_DEFAULT:-""}}
HARBOR_USER=${HARBOR_USER:-${HARBOR_USER_DEFAULT:-""}}
HARBOR_PASSWORD=${HARBOR_PASSWORD:-${HARBOR_PASSWORD_DEFAULT:-""}}

# 默认日志配置
LOG_LEVEL="DEBUG"
LOG_FILE="logs/build-tomcat.log"

# 初始化日志系统
init_logger "$LOG_LEVEL" "$LOG_FILE"

log_info "开始导入依赖库..."
log_debug "依赖库已导入:"
log_debug "- logger.sh"
log_debug "- utils.sh"
log_debug "- docker.sh"
log_debug "- download.sh"
log_info "✓ 依赖库导入完成"

# 加载配置文件（如果存在）
if [[ -f "${CONFIG_DIR}/build-tomcat.conf" ]]; then
    source "${CONFIG_DIR}/build-tomcat.conf"
fi

# 显示Harbor配置信息函数
show_harbor_config() {
    log_info "Harbor仓库配置:"
    if [ -z "$HARBOR_REGISTRY" ]; then
        log_error "Harbor仓库地址未配置"
        log_error "请通过以下方式之一设置Harbor仓库地址:"
        log_error "  1. 设置环境变量: export HARBOR_REGISTRY=your-harbor-registry:port"
        log_error "  2. 创建配置文件: ${CONFIG_FILE}"
        log_error "     配置文件内容示例: HARBOR_REGISTRY_DEFAULT=\"your-harbor-registry:port\""
        log_error "  3. 使用命令行参数: --harbor-registry your-harbor-registry:port"
        return 1
    else
        log_info "- Harbor仓库地址: $HARBOR_REGISTRY"
        if [ -n "$HARBOR_USER" ] && [ -n "$HARBOR_PASSWORD" ]; then
            log_info "- Harbor凭证: 已配置"
        else
            log_warn "- Harbor凭证: 未配置（可能无法推送镜像）"
        fi
        return 0
    fi
}

# 登录Harbor仓库函数
login_harbor_registry() {
    if [ -z "$HARBOR_REGISTRY" ]; then
        log_error "Harbor仓库地址未配置"
        return 1
    fi
    
    if [ -z "${HARBOR_USER}" ] || [ -z "${HARBOR_PASSWORD}" ]; then
        log_warn "Harbor用户名或密码未配置，镜像可能无法推送"
        log_warn "请通过以下方式之一设置Harbor凭证:"
        log_warn "  1. 设置环境变量: export HARBOR_USER=your-username HARBOR_PASSWORD=your-password"
        log_warn "  2. 在配置文件中设置: ${CONFIG_FILE}"
        log_warn "     配置文件内容: HARBOR_USER_DEFAULT=\"your-username\" HARBOR_PASSWORD_DEFAULT=\"your-password\""
        log_warn "  3. 使用命令行参数: --harbor-user your-username --harbor-password your-password"
        return 1
    fi

    log_info "正在登录Harbor仓库: ${HARBOR_REGISTRY}"
    printf "%s" "${HARBOR_PASSWORD}" | docker login "${HARBOR_REGISTRY}" -u "${HARBOR_USER}" --password-stdin
    local login_result=$?
    if [ $login_result -eq 0 ]; then
        log_info "Harbor登录成功"
    else
        log_error "Harbor登录失败"
    fi
    return $login_result
}

# 显示帮助信息
show_help() {
    # 从配置文件获取默认值和支持的版本
    local versions_file="${SCRIPT_DIR}/versions/tomcat_versions.json"
    local default_version
    default_version=$(jq -r '.default_version' "$versions_file")
    local supported_versions
    supported_versions=$(jq -r '.versions | keys | join(", ")' "$versions_file")
    local supported_jdk_versions
    supported_jdk_versions=$(jq -r '.jdk_compatibility | values | flatten | unique | sort | join(", ")' "$versions_file")

    # 获取默认JDK版本（从支持的JDK版本中取第一个）
    local default_jdk_version
    default_jdk_version=$(echo "$supported_jdk_versions" | cut -d',' -f1 | tr -d ' ')

    cat << EOF
用法: $(basename "$0") [选项]

选项:
  -h, --help                 显示帮助信息
  --list-archs              显示支持的架构列表
  -j, --jdk VERSION         指定JDK版本 (默认: ${default_jdk_version})
                           支持的版本: ${supported_jdk_versions}
  -t, --tomcat VERSION      指定Tomcat版本 (默认: ${default_version})
                           支持的版本: ${supported_versions}
  -o, --output-dir DIR      将镜像保存到指定目录
  -d, --debug              启用调试模式
  -p, --push               构建后推送镜像到Harbor仓库
  -v, --variant VARIANT    指定构建变体 (如: crypto)
  --harbor-registry URL    指定Harbor仓库地址
  --harbor-user USER       指定Harbor用户名
  --harbor-password PASS   指定Harbor密码
  --arch ARCH             指定构建架构 (amd64, arm64)
                           如未指定，将自动检测宿主机架构
  --log-level LEVEL       指定日志级别 (DEBUG, INFO, WARN, ERROR)
EOF
}

# 早期参数解析（处理不需要配置文件的选项）
parse_early_args() {
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                show_help
                exit 0
                ;;
            --list-archs)
                echo "支持的架构列表:"
                echo "  - amd64"
                echo "  - arm64"
                echo ""
                echo "当前系统架构: $(get_system_arch)"
                exit 0
                ;;
            *)
                # 跳过其他参数，留给主解析函数处理
                shift
                ;;
        esac
    done
}

# 解析命令行参数
parse_args() {
    # 默认值 - 如果全局变量未设置，使用临时默认值
    TOMCAT_VERSION="${DEFAULT_TOMCAT_VERSION:-}"
    JDK_VERSION="${DEFAULT_JDK_VERSION:-}"
    VARIANT="${DEFAULT_VARIANT:-default}"
    OUTPUT_DIR=""
    DEBUG="false"
    PUSH="false"
    ARCH=""
    
    while [[ $# -gt 0 ]]; do
        case "$1" in

            -j|--jdk)
                JDK_VERSION="$2"
                shift 2
                ;;
            -t|--tomcat)
                TOMCAT_VERSION="$2"
                shift 2
                ;;
            -o|--output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -d|--debug)
                DEBUG="true"
                LOG_LEVEL="DEBUG"
                shift
                ;;
            -p|--push)
                PUSH="true"
                shift
                ;;
            -v|--variant)
                VARIANT="$2"
                shift 2
                ;;
            --harbor-registry)
                HARBOR_REGISTRY="$2"
                shift 2
                ;;
            --harbor-user)
                HARBOR_USER="$2"
                shift 2
                ;;
            --harbor-password)
                HARBOR_PASSWORD="$2"
                shift 2
                ;;
            --arch)
                ARCH="$2"
                shift 2
                ;;
            --log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            *)
                echo "未知选项: $1" >&2
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    # 验证JDK版本
    if [[ ! " ${SUPPORTED_JDK_VERSIONS[*]} " =~ " ${JDK_VERSION} " ]]; then
        log_error "不支持的JDK版本: $JDK_VERSION"
        log_error "支持的版本: ${SUPPORTED_JDK_VERSIONS[*]}"
        exit 1
    fi
    
    # 验证Tomcat版本
    if ! validate_version "$TOMCAT_VERSION"; then
        exit 1
    fi
    
    # 验证变体
    if [[ ! " ${SUPPORTED_VARIANTS[*]} " =~ " ${VARIANT} " ]]; then
        log_error "不支持的变体: $VARIANT"
        log_error "支持的变体: ${SUPPORTED_VARIANTS[*]}"
        exit 1
    fi
    
    # 验证架构
    if [[ -n "$ARCH" ]] && [[ ! " ${SUPPORTED_ARCHITECTURES[*]} " =~ " ${ARCH} " ]]; then
        log_error "不支持的架构: $ARCH"
        log_error "支持的架构: ${SUPPORTED_ARCHITECTURES[*]}"
        exit 1
    fi
    
    # 如果未指定架构，使用当前系统架构
    if [[ -z "$ARCH" ]]; then
        log_info "未指定架构参数，正在自动检测宿主机架构..."
        ARCH=$(get_system_arch)
        if [[ "$ARCH" == "unsupported" ]]; then
            log_error "不支持的系统架构，请使用 --arch 参数手动指定支持的架构"
            log_error "支持的架构: ${SUPPORTED_ARCHITECTURES[*]}"
            exit 1
        fi
        log_info "自动检测到架构: $ARCH"
    else
        log_debug "使用用户指定的架构: $ARCH"
    fi
    
    # 如果需要推送镜像，则验证Harbor配置
    if [[ "$PUSH" == "true" ]]; then
        if [ -z "$HARBOR_REGISTRY" ]; then
            log_error "未提供Harbor仓库地址，推送镜像时需要设置 --harbor-registry"
            exit 1
        fi
        
        if [ -z "${HARBOR_USER}" ]; then
            log_error "未提供Harbor用户名，推送镜像时需要设置 --harbor-user"
            exit 1
        fi
        
        if [ -z "${HARBOR_PASSWORD}" ]; then
            log_error "未提供Harbor密码，推送镜像时需要设置 --harbor-password"
            exit 1
        fi
    fi
}

# 准备构建环境
prepare_build_env() {
    log_info "准备构建环境..."
    
    # 检查必需的命令
    log_debug "检查必需命令..."
    local required_cmds=(docker jq curl)
    for cmd in "${required_cmds[@]}"; do
        log_debug "检查命令: $cmd"
        if ! command -v "$cmd" >/dev/null 2>&1; then
            log_error "命令未找到: $cmd"
            return 1
        fi
        log_debug "✓ 命令可用: $cmd"
    done
    
    # 检查Docker守护进程
    if ! check_docker_daemon; then
        log_error "Docker 守护进程检查失败"
        return 1
    fi
    
    # 加载版本配置文件
    local versions_file="${SCRIPT_DIR}/versions/tomcat_versions.json"
    log_debug "加载版本配置文件: $versions_file"
    if [[ ! -f "$versions_file" ]]; then
        log_error "版本配置文件不存在: $versions_file"
        return 1
    fi
    
    # 读取Harbor配置
    log_debug "从配置文件读取Harbor配置..."
    local build_defaults
    build_defaults=$(jq -r '.build_defaults' "$versions_file")
    
    # 设置Harbor配置
    log_debug "检查Harbor配置..."
    if [[ -z "${HARBOR_REGISTRY:-}" ]]; then
        HARBOR_REGISTRY=$(echo "$build_defaults" | jq -r '.registry')
        log_debug "使用配置文件中的Harbor仓库: $HARBOR_REGISTRY"
    fi
    
    # 设置其他构建默认值
    BUILD_DEFAULTS[NAMESPACE]=$(echo "$build_defaults" | jq -r '.namespace')
    BUILD_DEFAULTS[REPOSITORY]=$(echo "$build_defaults" | jq -r '.repository')
    
    log_debug "构建默认值:"
    log_debug "- 命名空间: ${BUILD_DEFAULTS[NAMESPACE]}"
    log_debug "- 仓库: ${BUILD_DEFAULTS[REPOSITORY]}"
    log_debug "- 注册表: $HARBOR_REGISTRY"
    
    # 显示Harbor配置信息
    show_harbor_config
    
    # 如果需要推送镜像，则检查Harbor凭证并登录
    if [[ "$PUSH" == "true" ]]; then
        log_debug "需要推送镜像，检查Harbor凭证..."
        if ! login_harbor_registry; then
            log_warn "Harbor登录失败，但仍将继续构建"
        fi
    fi
    
    # 创建必要的目录
    log_debug "创建必要的目录..."
    local dirs=(
        "$(dirname "${LOG_FILE}")"
        "${PROJECT_ROOT}/docker/infrastructure/tomcat/resources"
    )
    
    for dir in "${dirs[@]}"; do
        log_debug "创建目录: $dir"
        if ! mkdir -p "$dir" 2>/dev/null; then
            log_error "无法创建目录: $dir"
            return 1
        fi
        log_debug "✓ 目录已创建/已存在: $dir"
    done
    
    log_info "✓ 构建环境准备完成"
    return 0
}

# 验证Tomcat版本
validate_version() {
    local version="$1"
    local versions_file="${SCRIPT_DIR}/versions/tomcat_versions.json"
    
    log_debug "验证Tomcat版本: $version"
    
    # 检查版本是否在配置文件中存在
    if ! jq -e --arg ver "$version" '.versions[$ver]' "$versions_file" >/dev/null; then
        log_error "不支持的Tomcat版本: $version"
        log_error "支持的版本: $(jq -r '.versions | keys | join(", ")' "$versions_file")"
        return 1
    fi
    
    # 验证JDK兼容性
    local major_version="${version%%.*}"
    local minor_version="${version#*.}"
    minor_version="${minor_version%%.*}"
    local tomcat_series="${major_version}.${minor_version}"
    
    log_debug "检查JDK兼容性: Tomcat $tomcat_series 与 JDK $JDK_VERSION"
    
    local compatible_jdks
    compatible_jdks=$(jq -r --arg series "$tomcat_series" '.jdk_compatibility[$series] | join(", ")' "$versions_file")
    
    if [[ "$compatible_jdks" == "null" ]]; then
        log_error "未找到Tomcat $tomcat_series 的JDK兼容性信息"
        return 1
    fi
    
    # 将兼容的JDK版本字符串转换为数组
    local -a compatible_jdk_array
    IFS=',' read -ra compatible_jdk_array <<< "$compatible_jdks"
    
    # 检查JDK版本是否兼容
    local is_compatible=false
    for ver in "${compatible_jdk_array[@]}"; do
        ver=$(echo "$ver" | tr -d ' ')  # 移除空格
        if [[ "$ver" == "$JDK_VERSION" ]]; then
            is_compatible=true
            break
        fi
    done
    
    if [[ "$is_compatible" != "true" ]]; then
        log_error "Tomcat $tomcat_series 不支持 JDK $JDK_VERSION"
        log_error "支持的JDK版本: $compatible_jdks"
        return 1
    fi
    
    log_debug "✓ 版本验证通过"
    return 0
}

# 生成Dockerfile
generate_dockerfile() {
    local version="$1"
    local jdk_version="$2"
    local variant="$3"
    local arch="$4"
    local template="${PROJECT_ROOT}/docker/infrastructure/tomcat/Dockerfile.template"
    local target="${PROJECT_ROOT}/docker/infrastructure/tomcat/Dockerfile"
    
    log_info "生成Dockerfile..."
    log_debug "使用模板: $template"
    
    # 检查模板文件是否存在
    if [[ ! -f "$template" ]]; then
        log_error "Dockerfile模板不存在: $template"
        return 1
    fi
    
    # 获取Tomcat主版本号
    local tomcat_major="${version%%.*}"
    
    # 获取SHA512校验和
    local tomcat_file="${PROJECT_ROOT}/docker/infrastructure/tomcat/resources/apache-tomcat-${version}.tar.gz"
    local tomcat_sha512=""
    if [[ -f "$tomcat_file" ]]; then
        tomcat_sha512=$(sha512sum "$tomcat_file" | cut -d' ' -f1)
    fi
    
    # 获取当前时间作为构建时间
    local build_date
    build_date=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 从配置文件获取加密组件版本（如果是crypto变体）
    local crypto_version=""
    if [[ "$variant" == "crypto" ]]; then
        local versions_file="${SCRIPT_DIR}/versions/tomcat_versions.json"
        crypto_version=$(jq -r '.crypto_version // empty' "$versions_file")
    fi
    
    # 获取Java类型、分发版和操作系统信息
    local java_type="jre"
    if jq -e --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_type' "$versions_file" >/dev/null; then
        java_type=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_type' "$versions_file")
    fi
    
    local java_distribution="temurin"
    if jq -e --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_distribution' "$versions_file" >/dev/null; then
        java_distribution=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_distribution' "$versions_file")
    fi
    
    local os_codename="jammy"
    if jq -e '.os_codename' "$versions_file" >/dev/null; then
        os_codename=$(jq -r '.os_codename' "$versions_file")
    fi
    
    # 获取OS后缀
    local os_suffix=""
    if jq -e --arg jdk "$jdk_version" '.jdk_configurations[$jdk].os_suffix' "$versions_file" >/dev/null; then
        os_suffix=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].os_suffix' "$versions_file")
    fi
    
    # 构建JDK基础镜像路径
    local base_image=""
    # 尝试获取完整的基础镜像配置
    if jq -e --arg jdk "$jdk_version" '.jdk_base_images[$jdk]' "$versions_file" >/dev/null; then
        base_image=$(jq -r --arg jdk "$jdk_version" '.jdk_base_images[$jdk]' "$versions_file")
        log_debug "使用预配置的基础镜像: $base_image"
    else
        log_error "未找到JDK ${jdk_version}的基础镜像配置"
        return 1
    fi
    
    # 从jdk_configurations获取配置
    local java_type=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_type' "$versions_file")
    local java_distribution=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_distribution' "$versions_file")
    local os_suffix=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].os_suffix' "$versions_file")
    
    log_debug "替换模板变量:"
    log_debug "- BASE_IMAGE: $base_image"
    log_debug "- TOMCAT_VERSION: $version"
    log_debug "- TOMCAT_MAJOR: $tomcat_major"
    log_debug "- TOMCAT_VARIANT: $variant"
    log_debug "- TOMCAT_SHA512: $tomcat_sha512"
    log_debug "- CRYPTO_FILES_VERSION: $crypto_version"
    log_debug "- JDK_VERSION: $jdk_version"
    log_debug "- BUILD_DATE: $build_date"
    log_debug "- ARCH: $arch"
    log_debug "- JAVA_TYPE: $java_type"
    log_debug "- JAVA_DISTRIBUTION: $java_distribution"
    log_debug "- OS_SUFFIX: $os_suffix"
    
    # 替换模板变量
    sed -e "s|\$BASE_IMAGE|$base_image|g" \
        -e "s|\$TOMCAT_VERSION|$version|g" \
        -e "s|\$TOMCAT_MAJOR|$tomcat_major|g" \
        -e "s|\$TOMCAT_VARIANT|$variant|g" \
        -e "s|\$TOMCAT_SHA512|$tomcat_sha512|g" \
        -e "s|\$CRYPTO_FILES_VERSION|$crypto_version|g" \
        -e "s|\$JDK_VERSION|$jdk_version|g" \
        -e "s|\$BUILD_DATE|$build_date|g" \
        -e "s|\$ARCH|$arch|g" \
        -e "s|\$JAVA_TYPE|$java_type|g" \
        -e "s|\$JAVA_DISTRIBUTION|$java_distribution|g" \
        "$template" > "$target"
    
    log_debug "✓ Dockerfile已生成: $target"
    return 0
}

# 构建Tomcat镜像
build_tomcat_image() {
    local tomcat_version="$1"
    local jdk_version="$2"
    local variant="$3"
    local arch="$4"
    
    log_info "构建Tomcat ${tomcat_version} (JDK ${jdk_version}, ${variant}, ${arch})..."
    
    # 生成Dockerfile
    if ! generate_dockerfile "$tomcat_version" "$jdk_version" "$variant" "$arch"; then
        log_error "生成Dockerfile失败"
        return 1
    fi
    
    # 准备构建参数
    local versions_file="${SCRIPT_DIR}/versions/tomcat_versions.json"
    
    # 从jdk_configurations获取配置
    local java_type=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_type' "$versions_file")
    local java_distribution=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].java_distribution' "$versions_file")
    local os_suffix=$(jq -r --arg jdk "$jdk_version" '.jdk_configurations[$jdk].os_suffix' "$versions_file")
    
    # 构建JDK基础镜像路径
    local base_image=""
    # 尝试获取完整的基础镜像配置
    if jq -e --arg jdk "$jdk_version" '.jdk_base_images[$jdk]' "$versions_file" >/dev/null; then
        base_image=$(jq -r --arg jdk "$jdk_version" '.jdk_base_images[$jdk]' "$versions_file")
        log_debug "使用预配置的基础镜像: $base_image"
    else
        log_error "未找到JDK ${jdk_version}的基础镜像配置"
        return 1
    fi
    
    # 生成符合Docker镜像命名规范的标签
    # 标签格式：9.0.100-jdk8-btit-jammy-crypto-sc34
    local tag="${tomcat_version}-${java_type}${jdk_version}-${java_distribution}-jammy"
    
    # 添加OS后缀（如果有）
    if [[ -n "$os_suffix" ]]; then
        tag="${tag}-${os_suffix}"
    fi
    
    # 从配置文件获取构建配置
    local build_defaults
    build_defaults=$(jq -r '.build_defaults' "$versions_file")
    
    # 获取支持的平台列表
    local platforms
    platforms=$(echo "$build_defaults" | jq -r '.platforms | join(",")')
    
    local image_name="${HARBOR_REGISTRY}/${BUILD_DEFAULTS[NAMESPACE]}/${BUILD_DEFAULTS[REPOSITORY]}:${tag}"
    local dockerfile="${PROJECT_ROOT}/docker/infrastructure/tomcat/Dockerfile"
    local build_args=(
        "--network=host"
        "--memory=4g"
        "--memory-swap=8g"
        "--build-arg" "BASE_IMAGE=${base_image}"
    )
    
    # 设置平台参数 - 移除多平台构建支持，只使用指定的架构或系统默认架构
    build_args+=("--platform" "$arch")
    
    # 构建镜像
    if ! build_docker_image "$image_name" "$dockerfile" "." "${build_args[@]}"; then
        log_error "镜像构建失败"
        return 1
    fi
    
    # 如果指定了输出目录，保存镜像
    if [[ -n "$OUTPUT_DIR" ]]; then
        local output_file="${OUTPUT_DIR}/tomcat-${tag}.tar"
        if ! save_docker_image "$image_name" "$output_file"; then
            log_error "保存镜像失败"
            return 1
        fi
    fi
    
    # 如果需要推送镜像
    if [[ "$PUSH" == "true" ]]; then
        log_info "推送镜像: $image_name"
        if ! docker push "$image_name"; then
            log_error "推送镜像失败: $image_name"
            return 1
        fi
        log_info "镜像推送成功: $image_name"
    fi
    
    log_info "镜像构建成功: $image_name"
    return 0
}

# 加载版本配置
load_versions() {
    # 检查版本配置文件是否存在
    if [ ! -f "$VERSION_FILE" ]; then
        echo "错误: 版本配置文件不存在: $VERSION_FILE"
        exit 1
    fi
    
    # 检查是否可以解析JSON
    if ! jq '.' "$VERSION_FILE" > /dev/null 2>&1; then
        echo "错误: 版本配置文件不是有效的JSON格式: $VERSION_FILE"
        exit 1
    fi
    
    # 获取支持的版本列表
    VERSIONS=($(jq -r '.versions | keys[]' "$VERSION_FILE"))
    if [ ${#VERSIONS[@]} -eq 0 ]; then
        echo "错误: 未在配置文件中找到支持的Tomcat版本"
        exit 1
    fi
    
    # 获取自动获取SHA512的配置
    AUTO_FETCH_SHA512=$(jq -r '.build_defaults.auto_fetch_sha512 // true' "$VERSION_FILE")
    
    echo "已加载版本配置:"
    echo "  支持的Tomcat版本: ${VERSIONS[*]}"
    echo "  自动获取SHA512: $AUTO_FETCH_SHA512"
}

# 主函数
main() {
    # 早期参数解析（处理不需要配置文件的选项）
    parse_early_args "$@"

    # 声明关联数组
    declare -A BUILD_DEFAULTS

    # 加载版本配置文件
    local versions_file="${SCRIPT_DIR}/versions/tomcat_versions.json"
    if [[ ! -f "$versions_file" ]]; then
        echo "错误: 版本配置文件不存在: $versions_file" >&2
        exit 1
    fi
    
    # 读取默认配置
    DEFAULT_TOMCAT_VERSION=$(jq -r '.default_version' "$versions_file")
    
    # 从配置文件读取支持的变体和架构
    SUPPORTED_VARIANTS=("default" "crypto")
    SUPPORTED_ARCHITECTURES=("amd64" "arm64")
    
    # 从配置文件读取支持的JDK版本
    readarray -t SUPPORTED_JDK_VERSIONS < <(jq -r '.jdk_compatibility | values | flatten | unique | sort | .[]' "$versions_file")

    # 设置默认JDK版本（使用支持版本中的第一个）
    DEFAULT_JDK_VERSION="${SUPPORTED_JDK_VERSIONS[0]}"
    DEFAULT_VARIANT="default"
    
    # 解析命令行参数
    parse_args "$@"

    # 如果解析后仍然为空，设置默认值
    if [[ -z "$TOMCAT_VERSION" ]]; then
        TOMCAT_VERSION="$DEFAULT_TOMCAT_VERSION"
    fi
    if [[ -z "$JDK_VERSION" ]]; then
        JDK_VERSION="$DEFAULT_JDK_VERSION"
    fi
    
    # 更新日志级别（如果在命令行参数中指定）
    if [[ "$DEBUG" == "true" ]]; then
        LOG_LEVEL="DEBUG"
        init_logger "$LOG_LEVEL" "$LOG_FILE"
    fi
    
    log_info "===== 开始构建Tomcat镜像 ====="
    log_info "使用以下配置:"
    log_info "- Tomcat版本: $TOMCAT_VERSION"
    log_info "- JDK版本: $JDK_VERSION"
    log_info "- 变体: $VARIANT"
    log_info "- 架构: $ARCH"
    log_info "- 调试模式: $DEBUG"
    log_info "- 推送镜像: $PUSH"
    if [[ "$PUSH" == "true" ]]; then
        log_info "- Harbor仓库: $HARBOR_REGISTRY"
    fi
    
    # 验证参数
    validate_args
    
    # 准备构建环境
    if ! prepare_build_env; then
        log_error "构建环境准备失败"
        exit 1
    fi
    
    log_info "准备下载Tomcat包..."
    log_debug "参数信息:"
    log_debug "- 版本: $TOMCAT_VERSION"
    log_debug "- 输出目录: ${PROJECT_ROOT}/docker/infrastructure/tomcat/resources"
    log_debug "- 配置文件: $versions_file"
    
    # 确保日志系统已初始化
    if [[ -z "${LOG_LEVEL:-}" ]]; then
        LOG_LEVEL="DEBUG"
        init_logger "$LOG_LEVEL" "$LOG_FILE"
    fi
    
    # 下载Tomcat包
    local tomcat_file
    if ! tomcat_file=$(download_tomcat "$TOMCAT_VERSION" "${PROJECT_ROOT}/docker/infrastructure/tomcat/resources" "$versions_file"); then
        log_error "下载Tomcat包失败"
        exit 1
    fi
    
    if [[ -z "$tomcat_file" ]] || [[ ! -f "$tomcat_file" ]]; then
        log_error "下载的Tomcat包无效: $tomcat_file"
        exit 1
    fi
    
    # 构建镜像
    if ! build_tomcat_image "$TOMCAT_VERSION" "$JDK_VERSION" "$VARIANT" "$ARCH"; then
        log_error "构建失败"
        exit 1
    fi
    
    # 构建完成后显示Summary
    log_info "===== 构建摘要 ====="
    
    # 获取标签组成部分
    local java_type="jre"
    if jq -e --arg jdk "$JDK_VERSION" '.jdk_configurations[$jdk].java_type' "$versions_file" >/dev/null; then
        java_type=$(jq -r --arg jdk "$JDK_VERSION" '.jdk_configurations[$jdk].java_type' "$versions_file")
    fi
    
    local java_distribution="temurin"
    if jq -e --arg jdk "$JDK_VERSION" '.jdk_configurations[$jdk].java_distribution' "$versions_file" >/dev/null; then
        java_distribution=$(jq -r --arg jdk "$JDK_VERSION" '.jdk_configurations[$jdk].java_distribution' "$versions_file")
    fi
    
    local os_codename="jammy"
    if jq -e '.os_codename' "$versions_file" >/dev/null; then
        os_codename=$(jq -r '.os_codename' "$versions_file")
    fi
    
    # 获取OS后缀
    local os_suffix=""
    if jq -e --arg jdk "$JDK_VERSION" '.jdk_configurations[$jdk].os_suffix' "$versions_file" >/dev/null; then
        os_suffix=$(jq -r --arg jdk "$JDK_VERSION" '.jdk_configurations[$jdk].os_suffix' "$versions_file")
    fi
    
    # 生成标签
    local tag="${TOMCAT_VERSION}-${java_type}${JDK_VERSION}-${java_distribution}-${os_codename}"
    if [[ -n "$os_suffix" ]]; then
        tag="${tag}-${os_suffix}"
    fi
    
    log_info "- 构建的镜像: ${HARBOR_REGISTRY}/${BUILD_DEFAULTS[NAMESPACE]}/${BUILD_DEFAULTS[REPOSITORY]}:${tag}"
    if [[ "$PUSH" == "true" ]]; then
        log_info "- 镜像已推送到Harbor仓库: $HARBOR_REGISTRY"
    fi
    if [[ -n "$OUTPUT_DIR" ]]; then
        log_info "- 镜像已保存到: ${OUTPUT_DIR}/tomcat-${tag}.tar"
    fi
    
    log_info "===== Tomcat镜像构建完成 ====="
}

# 执行主函数
main "$@"
